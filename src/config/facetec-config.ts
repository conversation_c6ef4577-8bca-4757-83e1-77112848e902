/**
 * Developer FaceTec config, the production
 * configuration will come from the backend!!!
 *
 * This should only be used in local dev env!
 */
const facetec = {
  PublicFaceScanEncryptionKey:
    '-----BEGIN PUBLIC KEY-----\n' +
    'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAv5QQ8S1jFF3S0SWlQqU9\n' +
    'A0pRU4FbJVUd1brGSk+CkJPr+AS/cBVcdBOSk8CdLwDP/bYHJ9viab3apZ5rYuH3\n' +
    'YSk75aDTRqb7j5QWQgpbvO2eEcr2UOXQKHSoA5oZj5EIOv59yJsd356IK4+myi7L\n' +
    'kN7ZGs53uei1KIRdZKIZTjcAZxa85V8Dm0TxbLgxAwKt2RtrKlYH0PcR5bh7t6E7\n' +
    'J7/lcm3/ERPlElUxNpOPXLlh2eFceYd797S1CCJMfEIBqlE445t1D6gAU47M8WXE\n' +
    '9AW9pXEMcNPdHr8oxmzdfFO/FePnvTgZYjh1JMpcHNohnQy1FqpYfmIEIAihQj9F\n' +
    'qwIDAQAB\n' +
    '-----END PUBLIC KEY-----',
  deviceKeyIdentifier: 'd4k2kPhkkYTwS4F426dPg4ZDCIFQVVFq',
  productionKey:
    '{  "domains": "tontine.com,tontinetrust.com,mytontine.com,5t4g1ng.robotontine.com",  "expiryDate": "2025-11-10",  "key": "003045022100e31568d2e5f5257af3b12eb88ec8f1d40d9741c1c21e5ac42afe7c7d513686b402204edbd3cf9c0ceabf1b848a90782ea6704871170962b5ae23b95f9358be1b6b18"}',
}

export { facetec }
